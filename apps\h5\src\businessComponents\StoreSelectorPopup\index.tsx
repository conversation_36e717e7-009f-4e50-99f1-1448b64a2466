'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import type { StoreListItem } from '@ninebot/core'
import { appLocalStorage, useToastContext } from '@ninebot/core'
import { useLocation } from '@ninebot/core/src/businessHooks'
import type { LocationInfo } from '@ninebot/core/src/types/amap'
import { Button, Dialog } from 'antd-mobile'

import { CustomPopup, Skeleton } from '@/components'

import AddressSelector from './AddressSelector'
import StoreEmpty from './StoreEmpty'
import StoreList from './StoreList'

// ==================== 类型定义 ====================

/**
 * 地址信息接口
 */
interface Address {
  region: string
  city: string
  district: string
}

/**
 * 组件状态枚举
 */
enum PopupState {
  LOADING = 'loading',
  LOCATION_DENIED = 'location_denied',
  ADDRESS_SELECTION = 'address_selection',
  STORE_SELECTION = 'store_selection',
}

/**
 * 组件属性接口
 */
interface StoreSelectorPopupProps {
  doorVisible: boolean
  setDoorVisible: (visible: boolean) => void
  selectStore: StoreListItem | null
  setSelectStore: (store: StoreListItem | null) => void
  setVisibleAddCartPop?: (visible: boolean) => void
  productId: string
}

// ==================== 常量定义 ====================

/**
 * 本地存储键名常量
 */
const STORAGE_KEYS = {
  USER_LOCATION: 'pdp_user_location',
  ADDRESS: 'pdp_address',
  SELECT_PRODUCT_OPTION_OPEN: 'select_product_option_open',
} as const

/**
 * 定位服务弹窗配置
 */
const LOCATION_MODAL_CONFIG = {
  content: '需要获取您的位置信息',
  cancelText: '取消',
  confirmText: '去设置',
} as const

// ==================== 图标组件 ====================

/**
 * 位置图标组件
 */
const LocationIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.07993 8.91421L4.49414 7.5L11.5652 14.5711L11.5652 15.9853L10.151 15.9853L3.07993 8.91421Z"
      fill="#DA291C"
    />
    <path
      d="M20.9201 8.91421L19.5059 7.5L12.4348 14.5711L12.4348 15.9853L13.849 15.9853L20.9201 8.91421Z"
      fill="#DA291C"
    />
  </svg>
)

// ==================== 主组件 ====================

/**
 * 门店选择器弹窗组件
 *
 * 功能：
 * 1. 自动获取用户位置并显示附近门店
 * 2. 支持手动选择地址
 * 3. 支持门店列表展示和选择
 * 4. 支持定位权限引导
 */
const StoreSelectorPopup = ({
  doorVisible,
  setDoorVisible,
  selectStore,
  setSelectStore,
  setVisibleAddCartPop,
  productId,
}: StoreSelectorPopupProps) => {
  const getI18nString = useTranslations('Common')
  const { getLocation, reverseGeocode, openBrowserSettings } = useLocation()
  const toast = useToastContext()

  // ==================== 状态管理 ====================

  // 组件状态
  const [popupState, setPopupState] = useState<PopupState>(PopupState.LOADING)

  // 数据状态
  const [storeList, setStoreList] = useState<StoreListItem[]>([])
  const [currentStore, setCurrentStore] = useState<StoreListItem | null>(null)
  const [address, setAddress] = useState<Address>({
    region: '',
    city: '',
    district: '',
  })

  // UI状态
  const [isLoading, setIsLoading] = useState(true)
  const [btnLoading, setBtnLoading] = useState(false)

  // ==================== 工具函数 ====================

  /**
   * 显示定位服务弹窗
   */
  const showLocationServiceModal = useCallback(() => {
    Dialog.confirm({
      bodyClassName: 'custom-dialog-confirm',
      content: LOCATION_MODAL_CONFIG.content,
      cancelText: LOCATION_MODAL_CONFIG.cancelText,
      confirmText: LOCATION_MODAL_CONFIG.confirmText,
      onConfirm: () => {
        setDoorVisible(false)
        openBrowserSettings()
      },
      onCancel: async () => {
        // 用户拒绝定位，进入手动选择地址模式
        await appLocalStorage.setItem(STORAGE_KEYS.USER_LOCATION, 1)
        setPopupState(PopupState.ADDRESS_SELECTION)
        setBtnLoading(true)
        setDoorVisible(true)
      },
    })
  }, [setDoorVisible, openBrowserSettings])

  /**
   * 保存地址到本地存储
   */
  const saveAddressToStorage = useCallback(async (addr: Address) => {
    await appLocalStorage.setItem(STORAGE_KEYS.ADDRESS, addr)
  }, [])

  /**
   * 从本地存储获取地址
   */
  const getAddressFromStorage = useCallback(async (): Promise<Address | null> => {
    return (await appLocalStorage.getItem(STORAGE_KEYS.ADDRESS)) as Address | null
  }, [])

  /**
   * 从本地存储获取位置信息
   */
  const getLocationFromStorage = useCallback(async (): Promise<LocationInfo | null> => {
    return (await appLocalStorage.getItem(STORAGE_KEYS.USER_LOCATION)) as LocationInfo | null
  }, [])

  /**
   * 保存位置信息到本地存储
   */
  const saveLocationToStorage = useCallback(async (location: LocationInfo) => {
    await appLocalStorage.setItem(STORAGE_KEYS.USER_LOCATION, location)
  }, [])

  // ==================== 事件处理函数 ====================

  /**
   * 获取用户位置
   */
  const handleGetLocation = useCallback(async () => {
    try {
      const result = await getLocation()

      if (result && result.latitude) {
        // 获取到定位信息，进入门店选择模式
        setPopupState(PopupState.STORE_SELECTION)
        setBtnLoading(true)
      } else {
        // 未授权定位信息，显示引导弹窗
        showLocationServiceModal()
      }
    } catch (error) {
      console.warn('获取定位信息失败，将使用手动选择地址:', error)

      // 定位失败时，进入手动选择地址模式
      await appLocalStorage.setItem(STORAGE_KEYS.USER_LOCATION, '')
      await appLocalStorage.removeItem(STORAGE_KEYS.ADDRESS)
      setPopupState(PopupState.ADDRESS_SELECTION)
      setBtnLoading(true)
      setDoorVisible(true)

      toast.show({
        icon: 'info',
        content: '定位失败，请手动选择门店地址',
      })
    }
  }, [getLocation, showLocationServiceModal, setDoorVisible, toast])

  /**
   * 关闭弹窗
   */
  const handleClosePop = useCallback(async () => {
    setDoorVisible(false)

    // 重置所有状态
    setPopupState(PopupState.LOADING)
    setStoreList([])
    setCurrentStore(null)
    setBtnLoading(false)
    setIsLoading(true)
    setAddress({
      region: '',
      city: '',
      district: '',
    })

    // 检查是否需要打开加购弹窗
    const isOpenAddToCart = await appLocalStorage.getItem(STORAGE_KEYS.SELECT_PRODUCT_OPTION_OPEN)
    if (isOpenAddToCart) {
      setVisibleAddCartPop?.(true)
      await appLocalStorage.removeItem(STORAGE_KEYS.SELECT_PRODUCT_OPTION_OPEN)
    }
  }, [setDoorVisible, setVisibleAddCartPop])

  /**
   * 确认门店选择
   */
  const handleConfirmStore = useCallback(async () => {
    if (currentStore) {
      // 更新选中的门店
      setSelectStore(currentStore)
    } else if (address.district && storeList.length === 0) {
      // 选择了地址但没有门店，直接关闭
      setDoorVisible(false)
      return
    } else {
      // 提示用户选择门店
      toast.show({
        icon: 'info',
        content: getI18nString('product_select_store_tip'),
      })
      return
    }

    setDoorVisible(false)

    // 检查是否需要打开加购弹窗
    const isOpenAddToCart = await appLocalStorage.getItem(STORAGE_KEYS.SELECT_PRODUCT_OPTION_OPEN)
    if (isOpenAddToCart) {
      setVisibleAddCartPop?.(true)
    }
  }, [
    currentStore,
    address.district,
    storeList.length,
    setSelectStore,
    setDoorVisible,
    toast,
    getI18nString,
    setVisibleAddCartPop,
  ])

  /**
   * 确认地址选择
   */
  const handleAddressConfirm = useCallback(async () => {
    await saveAddressToStorage(address)
    setStoreList([])
    setBtnLoading(true)
    setPopupState(PopupState.STORE_SELECTION)
  }, [address, saveAddressToStorage])

  /**
   * 显示地址选择器
   */
  const showAddressPop = useCallback(() => {
    setPopupState(PopupState.ADDRESS_SELECTION)
  }, [])

  /**
   * 地址级联选择回调
   */
  const handleAddressChange = useCallback(
    (province: { label: string }, city: { label: string }, district: { label: string }) => {
      const hasProvince = !!province?.label
      setBtnLoading(!hasProvince)

      setAddress({
        region: province?.label || '',
        city: city?.label || '',
        district: district?.label || '',
      })
    },
    [],
  )

  // ==================== 计算属性 ====================

  /**
   * 当前是否显示地址选择器
   */
  const showAddressSelector = useMemo(() => {
    return popupState === PopupState.ADDRESS_SELECTION
  }, [popupState])

  /**
   * 当前是否显示门店列表
   */
  const showStoreList = useMemo(() => {
    return popupState === PopupState.STORE_SELECTION
  }, [popupState])

  /**
   * 当前是否显示定位引导
   */
  const showLocationGuide = useMemo(() => {
    return popupState === PopupState.LOCATION_DENIED
  }, [popupState])

  /**
   * 按钮文本
   */
  const buttonText = useMemo(() => {
    if (showLocationGuide) return getI18nString('product_get_location')
    return getI18nString('confirm')
  }, [showLocationGuide, getI18nString])

  /**
   * 按钮点击处理函数
   */
  const buttonClickHandler = useMemo(() => {
    if (showLocationGuide) return handleGetLocation
    if (showStoreList) return handleConfirmStore
    return handleAddressConfirm
  }, [
    showLocationGuide,
    showStoreList,
    handleGetLocation,
    handleConfirmStore,
    handleAddressConfirm,
  ])

  /**
   * 按钮是否禁用
   */
  const isButtonDisabled = useMemo(() => {
    if (showLocationGuide) return false
    if (showStoreList) return btnLoading || !currentStore
    return btnLoading
  }, [showLocationGuide, showStoreList, btnLoading, currentStore])

  // ==================== 初始化逻辑 ====================

  /**
   * 初始化组件
   */
  const initializeComponent = useCallback(async () => {
    try {
      setStoreList([])
      setIsLoading(true)

      // 获取本地存储的地址和位置信息
      const [pdpAddress, pdpLocation] = await Promise.all([
        getAddressFromStorage(),
        getLocationFromStorage(),
      ])

      // 尝试获取当前位置
      let currentLocation: LocationInfo | null = pdpLocation
      if (!currentLocation) {
        try {
          currentLocation = await getLocation()
        } catch (error) {
          console.warn('获取位置信息失败，将使用手动选择地址:', error)
          currentLocation = null
        }
      }

      // 保存有效的位置信息
      if (currentLocation && currentLocation.latitude && currentLocation.longitude) {
        await saveLocationToStorage(currentLocation)
      }

      // 同步当前选中的门店
      setCurrentStore(selectStore)

      if (pdpAddress) {
        // 有保存的地址，直接显示门店列表
        setAddress(pdpAddress)
        setPopupState(PopupState.STORE_SELECTION)
      } else if (currentLocation && currentLocation.latitude && currentLocation.longitude) {
        try {
          // 通过逆地理编码获取地址
          const res = await reverseGeocode(currentLocation.latitude, currentLocation.longitude)
          const addr = {
            region: res.regeocode.addressComponent.province,
            city: res.regeocode.addressComponent.city,
            district: res.regeocode.addressComponent.district,
          }

          await saveAddressToStorage(addr)
          setAddress(addr)
          setPopupState(PopupState.STORE_SELECTION)
        } catch (reverseError) {
          console.warn('逆地理编码失败，将使用手动选择地址:', reverseError)
          // 逆地理编码失败，显示地址选择器
          setPopupState(PopupState.ADDRESS_SELECTION)
          setBtnLoading(true)
        }
      } else {
        // 没有位置信息，直接进入地址选择模式
        await Promise.all([
          appLocalStorage.removeItem(STORAGE_KEYS.USER_LOCATION),
          appLocalStorage.removeItem(STORAGE_KEYS.ADDRESS),
        ])

        setPopupState(PopupState.ADDRESS_SELECTION)
        setBtnLoading(true)
        setAddress({
          region: '',
          city: '',
          district: '',
        })
      }
    } catch (error) {
      console.error('初始化门店选择器失败:', error)
      toast.show({
        icon: 'fail',
        content: '定位失败，请手动选择门店',
      })

      // 初始化失败，进入地址选择模式
      await appLocalStorage.setItem(STORAGE_KEYS.USER_LOCATION, '')
      setPopupState(PopupState.ADDRESS_SELECTION)
      setBtnLoading(true)
      setDoorVisible(true)
    } finally {
      setIsLoading(false)
    }
  }, [
    getAddressFromStorage,
    getLocationFromStorage,
    getLocation,
    saveLocationToStorage,
    selectStore,
    reverseGeocode,
    saveAddressToStorage,
    toast,
    setDoorVisible,
  ])

  // ==================== 副作用 ====================

  /**
   * 监听弹窗显示状态，初始化组件
   */
  useEffect(() => {
    if (doorVisible) {
      initializeComponent()
    }
  }, [doorVisible, initializeComponent])

  // ==================== 渲染函数 ====================

  /**
   * 渲染加载状态
   */
  const renderLoadingState = () => (
    <div className="flex h-full flex-col p-4">
      {Array.from({ length: 5 }, (_, index) => (
        <Skeleton
          key={index}
          style={{
            borderRadius: 12,
            height: 60,
            width: '100%',
            marginTop: 12,
          }}
        />
      ))}
    </div>
  )

  /**
   * 渲染定位引导状态
   */
  const renderLocationGuideState = () => (
    <div className="flex h-full flex-col items-center justify-center">
      <StoreEmpty isStoreSelector onOpenAddressPop={showAddressPop} />
    </div>
  )

  /**
   * 渲染主要内容
   */
  const renderMainContent = () => (
    <div className="flex h-full flex-col">
      {/* 地址显示区域 */}
      {address?.region && (
        <div
          className="flex h-base-64 items-center justify-between gap-base p-8 text-lg"
          onClick={showAddressPop}>
          <div className="flex items-center">
            {getI18nString('product_nearby_stores')}：
            <span className="text-primary">
              {address?.region} {address?.city} {address?.district}
            </span>
          </div>
          {showStoreList && <LocationIcon />}
        </div>
      )}

      {/* 门店列表 */}
      {showStoreList && (
        <StoreList
          key={`${address.region}-${address.city}-${address.district}`}
          store={storeList}
          setStore={(list) =>
            setStoreList((list as (StoreListItem | null)[]).filter(Boolean) as StoreListItem[])
          }
          productId={productId}
          showAddressPop={showAddressPop}
          setBtnLoading={setBtnLoading}
          curStore={currentStore}
          setCurStore={setCurrentStore}
          address={address}
        />
      )}

      {/* 地址选择器 */}
      {showAddressSelector && (
        <AddressSelector
          onSelect={handleAddressChange}
          defaultValue={{
            province: address.region,
            city: address.city,
            district: address.district,
          }}
        />
      )}
    </div>
  )

  /**
   * 渲染内容
   */
  const renderContent = () => {
    if (isLoading) {
      return renderLoadingState()
    }

    if (showLocationGuide) {
      return renderLocationGuideState()
    }

    return renderMainContent()
  }

  // ==================== 组件返回 ====================

  return (
    <CustomPopup
      showHeader
      headTitle={getI18nString('product_select_store')}
      visible={doorVisible}
      onClose={handleClosePop}
      bodyStyle={{ height: '60rem' }}
      footer={
        <div className="fixed bottom-0 left-0 right-0 z-10 flex h-[90px] items-center justify-center bg-white px-[24px]">
          <Button
            color="primary"
            className="nb-button !h-[40px] w-full"
            disabled={isButtonDisabled}
            onClick={buttonClickHandler}>
            <span className="font-miSansDemiBold450 text-[14px] leading-none">{buttonText}</span>
          </Button>
        </div>
      }>
      {renderContent()}
    </CustomPopup>
  )
}

export default StoreSelectorPopup
